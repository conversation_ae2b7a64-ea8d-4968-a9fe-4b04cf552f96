"""
Lightweight Intake Agent handler for CopilotKit integration.

This avoids heavy imports at startup and lazily loads the IntakeAgent
from the pi_lawyer package only when requests arrive.
"""

from __future__ import annotations

import logging
import uuid
from typing import Any, Dict

logger = logging.getLogger(__name__)


class IntakeCopilotHandler:
  """Handle CopilotKit requests for the Intake Agent with lazy imports."""

  def __init__(self) -> None:
    self._agent = None
    self._initialized = False

  def _ensure_initialized(self) -> None:
    if self._initialized:
      return
    try:
      # Load environment variables first
      import os
      from dotenv import load_dotenv

      # Load .env file from project root
      env_path = os.path.join(os.path.dirname(__file__), '../../../../.env')
      if os.path.exists(env_path):
        load_dotenv(env_path, override=True)
        logger.info("Loaded environment variables from .env")

      # Try different import paths depending on how the server is started
      IntakeAgent = None

      # First try: relative import from backend directory
      try:
        from .agent import IntakeAgent
        logger.info("Loaded IntakeAgent via relative import")
      except ImportError:
        # Second try: absolute import with backend prefix
        try:
          from backend.agents.interactive.intake.agent import IntakeAgent
          logger.info("Loaded IntakeAgent via backend prefix import")
        except ImportError:
          # Third try: direct import from agents
          try:
            from agents.interactive.intake.agent import IntakeAgent
            logger.info("Loaded IntakeAgent via agents import")
          except ImportError as e:
            raise ImportError(f"Could not import IntakeAgent: {e}")

      self._agent = IntakeAgent()
      self._initialized = True
      logger.info("Intake agent initialized successfully")
    except Exception as primary_err:
      # Log error and raise - no fallback to legacy agents
      logger.error(
        "Failed to initialize IntakeAgent: %s",
        primary_err,
      )
      raise RuntimeError(f"IntakeAgent initialization failed: {primary_err}") from primary_err

  async def handle_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
    """Process an Intake Agent request and return CopilotKit-compatible response."""
    self._ensure_initialized()

    thread_id = data.get("threadId") or str(uuid.uuid4())
    user_id = data.get("userId") or "anonymous"
    tenant_id = data.get("tenantId") or "default"

    # Build full message history instead of only last user message
    raw_msgs = data.get("messages") or []
    state_messages = []
    for m in raw_msgs:
      role = (m.get("role") or m.get("type") or "").lower()
      content = m.get("content")
      # Normalize content to string
      if isinstance(content, list):
        content = " ".join([str(x) for x in content if isinstance(x, str)])
      elif content is None:
        content = ""
      else:
        content = str(content)
      if role in ("user", "human"):
        state_messages.append({"type": "human", "content": content})
      elif role in ("assistant", "ai"):
        state_messages.append({"type": "ai", "content": content})
      elif role == "system":
        state_messages.append({"type": "system", "content": content})

    # Ensure at least one human message exists to kick things off
    if not state_messages:
      state_messages = [{"type": "human", "content": "Hello"}]

    # If agent failed to initialize, return a graceful response
    if not self._agent:
      return {
        "messages": [
          {
            "role": "assistant",
            "content": [
              "Intake agent is temporarily unavailable. Please try again later."
            ],
          }
        ],
        "done": True,
        "threadId": thread_id,
      }

    try:
      # Build state in the minimal format IntakeAgent expects
      state = {"messages": state_messages}

      # Staff mode by default for admin pages
      if data.get("context") == "intake" or data.get("agent") == "intake_agent":
        state["intake_mode"] = "staff"

      # CRITICAL: Enable progressive capture for CopilotKit requests
      state["enable_progressive_capture"] = True

      # Initialize progressive capture state if not present
      if "progressive_capture" not in state:
        state["progressive_capture"] = {
          "enabled": True,
          "current_field": None,
          "collected_fields": {},
          "field_order": [
            "full_name", "email", "phone", "incident_date", "incident_location",
            "injury_description", "other_parties", "insurance_info",
            "previous_attorney", "case_urgency"
          ]
        }

      # Initialize matter data structure
      if "matter" not in state:
        state["matter"] = {}

      config = {
        "configurable": {
          "thread_id": thread_id,
          "tenant_id": tenant_id,
          "user_id": user_id,
        }
      }

      logger.info(f"CopilotKit: Executing intake agent with progressive_capture enabled for thread {thread_id}")

      # Use the execute method directly to trigger progressive capture logic
      result_state = await self._agent.execute(state, config)

      # Extract last assistant/ai message
      assistant_text = ""
      rmsgs = result_state.get("messages") if isinstance(result_state, dict) else []
      if isinstance(rmsgs, list):
        for m in reversed(rmsgs):
          role = (m.get("role") or m.get("type") or "").lower() if isinstance(m, dict) else ""
          content = m.get("content") if isinstance(m, dict) else None
          if role in ("assistant", "ai") and isinstance(content, str) and content.strip():
            assistant_text = content.strip()
            break

      if not assistant_text:
        assistant_text = "I collected your intake details. What else can I help with?"

      # Debug logging for progressive capture
      progressive_state = result_state.get("progressive_capture", {})
      current_field = progressive_state.get("current_field")
      collected_fields = progressive_state.get("collected_fields", {})

      logger.info(f"CopilotKit: Progressive capture state - current_field: {current_field}, collected: {list(collected_fields.keys())}")
      logger.info(f"CopilotKit: Assistant response: {assistant_text[:100]}...")

      return {
        "messages": [{"role": "assistant", "content": [assistant_text]}],
        "done": True,
        "threadId": thread_id,
      }

    except Exception as e:
      logger.exception("Intake agent invocation failed: %s", e)
      return {
        "messages": [
          {
            "role": "assistant",
            "content": [
              "I ran into an error running the intake flow. Please try again."
            ],
          }
        ],
        "done": True,
        "threadId": thread_id,
      }
